"use client";
import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { User, Mail, Phone } from "lucide-react";
import { Patient, Provider } from "@/types/chat";

interface ParticipantsPopupProps {
  isOpen: boolean;
  onClose: () => void;
  participants: {
    patient: Patient;
    provider: Provider;
  };
}

const ParticipantsPopup: React.FC<ParticipantsPopupProps> = ({
  isOpen,
  onClose,
  participants,
}) => {
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.charAt(0) || ""}${lastName?.charAt(0) || ""}`.toUpperCase();
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "USER":
        return "default";
      case "BUSER":
      case "PROVIDER":
        return "secondary";
      default:
        return "outline";
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case "USER":
        return "Patient";
      case "BUSER":
        return "Provider";
      case "PROVIDER":
        return "Provider";
      default:
        return role;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Chat Participants
          </DialogTitle>
          <DialogDescription>
            People participating in this conversation
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {/* Patient */}
          <div className="flex items-center space-x-4 p-3 rounded-lg border bg-card">
            <Avatar className="h-12 w-12">
              <AvatarImage src="/images/patient.png" alt="Patient" />
              <AvatarFallback className="bg-blue-100 text-blue-600">
                {getInitials(participants.patient.first_name, participants.patient.last_name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-1">
              <div className="flex items-center gap-2">
                <h4 className="text-sm font-semibold">
                  {participants.patient.first_name} {participants.patient.last_name}
                </h4>
                <Badge variant={getRoleBadgeVariant(participants.patient.role || "USER")}>
                  {getRoleDisplayName(participants.patient.role || "USER")}
                </Badge>
              </div>
              {participants.patient.email && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Mail className="h-3 w-3" />
                  {participants.patient.email}
                </div>
              )}
              {participants.patient.phone && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Phone className="h-3 w-3" />
                  {participants.patient.phone}
                </div>
              )}
            </div>
          </div>

          {/* Provider */}
          <div className="flex items-center space-x-4 p-3 rounded-lg border bg-card">
            <Avatar className="h-12 w-12">
              <AvatarImage src="/images/provider.png" alt="Provider" />
              <AvatarFallback className="bg-green-100 text-green-600">
                {getInitials(participants.provider.first_name, participants.provider.last_name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-1">
              <div className="flex items-center gap-2">
                <h4 className="text-sm font-semibold">
                  {participants.provider.first_name} {participants.provider.last_name}
                </h4>
                <Badge variant={getRoleBadgeVariant(participants.provider.role || "PROVIDER")}>
                  {getRoleDisplayName(participants.provider.role || "PROVIDER")}
                </Badge>
              </div>
              {participants.provider.email && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Mail className="h-3 w-3" />
                  {participants.provider.email}
                </div>
              )}
              {participants.provider.phone && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Phone className="h-3 w-3" />
                  {participants.provider.phone}
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ParticipantsPopup;
