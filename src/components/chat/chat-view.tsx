"use client";
import { SocketContext } from "@/context/socket";
import {
  <PERSON><PERSON>,
  Chat<PERSON>ontainer,
  ConversationHeader,
  Message,
  MessageInput,
  MessageList,
} from "@chatscope/chat-ui-kit-react";
import { CheckCircle } from "lucide-react";
import React, { useContext, useEffect, useState } from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import { Dialog, DialogContent } from "../ui/dialog";
import FileDisplayContainer from "@/components/fileDisplayContainer";
import { apiClient } from "@/lib/api";
import { Loader } from "../shared/loader";
import { toast } from "sonner";
import { validateFile, isImageType, formatChatMessageDate } from "@/lib/utils";
import { patientImage, providerImage } from "@/assets";
import useNotification from "@/hooks/useNotification";
import LogoutButton from "@/components/auth/logout-button";
import { Switch } from "../ui/switch";
import { ChatMetadata } from "@/types/chat";
import ParticipantsPopup from "./participants-popup";
import { Users } from "lucide-react";
import { Button } from "../ui/button";
interface User {
  user_id: string;
  first_name: string;
  last_name: number;
  messageHistory: any[];
}

interface Props {
  patient: User;
  provider: User;
  room: any;
  role: "USER" | "PROVIDER";
  token: string;
  roomIdentifier: string;
}

const ChatViewPage = ({
  patient,
  provider,
  room,
  role,
  token,
  roomIdentifier,
}: Props) => {
  const providerFullName = provider?.first_name + " " + provider?.last_name;
  const patientFullName = patient?.first_name + " " + patient?.last_name;
  const receiverFullName =
    role === "PROVIDER" ? patientFullName : providerFullName;
  const senderFullName =
    role === "PROVIDER" ? providerFullName : patientFullName;
  const roomId = room?.id;
  const { socket, isConnected } = useContext(SocketContext);
  const [messageHistory, setMessageHistory] = useState<any[]>();
  const currentUserId = role === "USER" ? patient?.user_id : provider?.user_id;
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [imageModalValue, setImageModalValue] = useState("");
  const [loader, setLoader] = useState(false);
  const senderImage = role === "USER" ? patientImage : providerImage;
  const receiverImage = role === "USER" ? providerImage : patientImage;
  const sendNotification = useNotification();
  const [chatMetadata, setChatMetadata] = useState<ChatMetadata | null>(null);
  const [participantsPopupVisible, setParticipantsPopupVisible] =
    useState(false);

  const getMessageHistory = async () => {
    setLoader(true);
    try {
      const res = await apiClient({
        method: "GET",
        endpoint: `chat/${roomIdentifier}/messages`,
        data: {},
        token: token || undefined,
      });
      if (res.statusCode === 200) {
        toast.success("Chat room initiated.", {
          duration: 1000,
          closeButton: true,
        });
        setMessageHistory(res.data?.messages ?? []);

        // Store chat metadata for header display
        if (res.data?.chatMetadata) {
          setChatMetadata(res.data.chatMetadata);
        }
      } else {
        toast(res.message, {
          closeButton: true,
          duration: 1000,
        });
      }
    } catch (error) {
      toast("Something went wrong.");
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  const readAllMessage = async () => {
    await apiClient({
      method: "GET",
      endpoint: `chat/${roomIdentifier}/read-all-messages`,
      data: {},
      token: token || undefined,
    });
  };

  useEffect(() => {
    getMessageHistory();
    readAllMessage();
  }, [roomId]);

  const handledMessageSend = async (message: string) => {
    const currentUser = role === "USER" ? patient : provider;
    socket?.emit("send-message", {
      message: message,
      type: "room",
      room_id: roomId,
      sender: {
        user_id: currentUserId,
        first_name: currentUser?.first_name,
        last_name: currentUser?.last_name,
        name: senderFullName,
      },
    });
    const newMessage = [
      ...(messageHistory ?? []),
      {
        message: message,
        type: "room",
        roomId: roomId,
        sender: {
          user_id: currentUserId,
          first_name: currentUser?.first_name,
          last_name: currentUser?.last_name,
          name: senderFullName,
        },
        created_at: new Date().toISOString(),
      },
    ];
    setMessageHistory(newMessage);
  };
  const handleOnAttached = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        uploadImage({
          target: { files: [file] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      }
    };
    input.click();
  };
  const uploadImage = async (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log(e.target.files);
    setLoader(true);
    if (!e.target.files) return;

    const file = e.target.files[0];
    const validation = validateFile(file);

    if (!validation.isValid) {
      toast(validation.error);
      setLoader(false);
      return;
    }

    try {
      const data = new FormData();
      data.append("file", e.target.files[0]);

      if (currentUserId && roomId) {
        data.append("user_id", currentUserId.toString());
        data.append("room_id", roomId?.toString() ?? "");
      }
      const res = await apiClient({
        method: "POST",
        endpoint: "files/upload",
        data,
        isFormData: true,
      });

      socket?.emit("send-message", {
        message: "ATTACHMENT",
        type: "room",
        file_path: res.fileUrl,
        file_id: res.fileId,
        room_id: roomId,
        sender: {
          user_id: currentUserId,
          name: senderFullName,
        },
      });

      setMessageHistory((prev) => [
        ...(prev ?? []),
        {
          sender_id: currentUserId,
          message: "ATTACHMENT",
          type: "room",
          file_id: res.fileId,
          file: {
            user_file_id: res.fileId,
            path: res.fileUrl,
          },
          sender: {
            user_id: currentUserId,
            name: senderFullName,
          },
        },
      ]);
    } catch (error) {
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };
  const getFileSignUrl = async (file_id: string) => {
    setLoader(true);

    try {
      // Use cookie-based token management instead of localStorage
      const { getAuthToken } = await import("@/lib/tokenManager");
      const token = await getAuthToken();
      const res = await apiClient({
        method: "POST",
        endpoint: "files/sign-url",
        data: { file_id },
        token: token || undefined,
      });
      setImageModalVisible(true);
      setImageModalValue(res.data.url);
    } catch (error) {
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    console.log(Boolean(socket && currentUserId && roomId));
    if (socket && currentUserId && roomId) {
      socket.emit("join", {
        room_id: roomId,
        user_id: currentUserId,
        name: senderFullName,
      });
    }
  }, [socket, currentUserId]);

  // socket?.on('receive-message', (data: any) => {
  //   const newMessage = [...(messageHistory ?? []), data];
  //   sendNotification({
  //     title: `New Message from ${receiverFullName}`,
  //     body: data.message,
  //   })
  //   setMessageHistory(newMessage);
  // });

  const toggleSwitch = async (e: boolean) => {
    try {
      setLoader(true);
      const res = await apiClient({
        method: "POST",
        endpoint: `room/${roomId}/${e ? "enable" : "disable"}`,
        data: {},
        token: token || undefined,
      });
    } catch (error) {
      setLoader(false);

      toast.error("Something went wrong while toggling switch.");
      return;
    } finally {
      setLoader(false);
      socket.emit("toggle-room-status", {
        room_id: roomId,
        user_id: currentUserId,
        patient_id: patient?.user_id,
        name: senderFullName,
        room_status: e ? "active" : "inactive",
      });
    }

    // Handle the switch toggle logic here
  };

  useEffect(() => {
    socket?.on("receive-message", (data: any) => {
      const newMessage = [...(messageHistory ?? []), data];
      sendNotification({
        title: `New Message from ${receiverFullName}`,
        body: data.message,
      });
      setMessageHistory(newMessage);
      readAllMessage();
    });
    return () => {
      socket?.off("receive-message");
    };
  }, [socket, messageHistory]);

  return (
    <>
      <Loader show={loader} />
      <Dialog
        open={imageModalVisible}
        onOpenChange={() => setImageModalVisible(false)}
      >
        <DialogContent className="sm:max-w-[925px]">
          <FileDisplayContainer
            src={imageModalValue}
            imageModalVisible={imageModalVisible}
            setImageModalVisible={setImageModalVisible}
          />
        </DialogContent>
      </Dialog>
      <ChatContainer
        style={{
          height: "100vh",
        }}
      >
        <ConversationHeader>
          <Avatar
            name={receiverFullName}
            src={`/images/${
              role === "PROVIDER" ? "patient.png" : "provider.png"
            }`}
          />
          <ConversationHeader.Content
            userName={receiverFullName}
            info={
              <div className="text-xs text-gray-500 space-y-1">
                {chatMetadata?.room?.description && (
                  <div>{chatMetadata.room.description}</div>
                )}
                {chatMetadata?.service?.display_service_name && (
                  <div className="font-medium text-blue-600">
                    Service: {chatMetadata.service.display_service_name}
                  </div>
                )}
              </div>
            }
          />
          <ConversationHeader.Actions className="flex items-center gap-2">
            {chatMetadata?.participants && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setParticipantsPopupVisible(true)}
                title="View Participants"
              >
                <Users className="h-4 w-4" />
              </Button>
            )}
            <Switch
              id="chat-switch"
              defaultChecked={room.active}
              onCheckedChange={(e) => {
                toggleSwitch(e);
              }}
            />
            {isConnected && <CheckCircle />}
            <LogoutButton variant="ghost" size="icon" showText={false} />
          </ConversationHeader.Actions>
        </ConversationHeader>
        <MessageList>
          {messageHistory &&
            messageHistory?.map((data, idx) => {
              const isSendedByMe = data.sender.user_id === currentUserId;
              const fullName =
                data.sender?.first_name && data.sender?.last_name
                  ? `${data.sender.first_name} ${data.sender.last_name}`
                  : data.sender?.name || "Unknown";
              const formattedTime = formatChatMessageDate(data.created_at);
              return (
                <Message
                  key={idx}
                  onClick={() => {
                    if (data.file?.path) {
                      getFileSignUrl(data.file?.path);
                    }
                  }}
                  model={{
                    direction: isSendedByMe ? "outgoing" : "incoming",
                    // message: data?.message?.replace(/<[^>]*>?/gm, ''),
                    position: "last",
                    sender: senderFullName,
                    sentTime: data?.created_at,
                    type: "html",
                  }}
                >
                  {data?.message === "ATTACHMENT" ? (
                    <Message.ImageContent
                      className="cursor-pointer"
                      src={`/images/${
                        isImageType(data.file?.path) ? "file.png" : "pdf.png"
                      }`}
                      height={100}
                    />
                  ) : (
                    <Message.HtmlContent
                      html={`<b>${fullName}: </b> <br/>${data?.message} <br/><em style='font-size:10px'>${formattedTime}</em>`}
                    />
                  )}
                  {/* {data?.message === "ATTACHMENT" ? <Message.ImageContent className='cursor-pointer' src={`/images/file.png`} height={100} /> : <Message.HtmlContent html={data?.message} />} */}
                  {isSendedByMe ? (
                    <></>
                  ) : (
                    <Avatar
                      name="Sumanta Dey"
                      src={`/images/${
                        role === "PROVIDER" ? "patient.png" : "provider.png"
                      }`}
                    />
                  )}
                </Message>
              );
            })}
        </MessageList>
        <MessageInput
          autoFocus
          placeholder="Type message here"
          onSend={handledMessageSend}
          onAttachClick={handleOnAttached}
        />
      </ChatContainer>

      {/* Participants Popup */}
      {chatMetadata?.participants && (
        <ParticipantsPopup
          isOpen={participantsPopupVisible}
          onClose={() => setParticipantsPopupVisible(false)}
          participants={chatMetadata.participants}
        />
      )}
    </>
  );
};

export default ChatViewPage;
