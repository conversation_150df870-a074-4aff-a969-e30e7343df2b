"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { apiClient } from "@/lib/api";
import { tokenManager } from "@/lib/tokenManager";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Loader } from "@/components/shared/loader";
import { OtpModal } from "@/components/shared/otp-modal";
import { Badge } from "@/components/ui/badge";
import LogoutButton from "@/components/auth/logout-button";
import { Calendar, Clock, User, MessageCircle } from "lucide-react";

interface Order {
  id: number;
  order_guid: string;
  service_id: number;
  provider_id: number | null;
  status: string;
  service_type: string;
  session_type: string;
  created_at: string;
  updated_at: string;
  prescription_delivery: boolean;
  ravkoo_prescription_option: boolean;
  pharmacy_details: {
    name: string;
    phone: string;
    address: string;
  };
  provider_details: {
    provider_id: number | null;
    name: string | null;
    first_name: string | null;
    last_name: string | null;
    email: string | null;
    phone: string | null;
    role: string | null;
  };
  service_details: {
    service_id: number;
    service_name: string;
    service_description: string;
    service_title: string;
    service_subtitle: string;
    service_key: string;
    service_details: any;
    service_mode: string;
    service_amount: number;
    display_service_name: string;
    is_video_call: boolean;
    is_audio_call: boolean;
  };
}

interface PatientData {
  patient: {
    user_id: number;
    name: string;
    email: string;
    role: string;
  };
  orders: Order[];
  total_orders: number;
}

interface ApiResponse<T = any> {
  statusCode: number;
  message: string;
  data?: T;
}

/**
 * Patient Consult List Component
 * Handles authentication and displays list of patient consultations
 */
const PatientConsultListComponent: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [email, setEmail] = useState("");
  const [role] = useState("USER"); // Default role as specified
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoadingOrders, setIsLoadingOrders] = useState(false);

  const router = useRouter();

  // Check authentication status on component mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    setIsLoading(true);
    try {
      const token = await tokenManager.getTokenWithMigration();
      if (token) {
        setIsAuthenticated(true);
        await loadOrders(token);
      } else {
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error("Failed to check auth status:", error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const sendOtp = async () => {
    if (!email.trim()) {
      toast.error("Please enter your email address");
      return;
    }

    setIsLoading(true);
    try {
      const response: ApiResponse = await apiClient({
        method: "POST",
        endpoint: "auth/send-otp-role-email",
        data: {
          role: role,
          email: email,
        },
      });

      if (response.statusCode === 200) {
        setShowOtpModal(true);
        toast.success(response.message || "OTP sent successfully");
      } else {
        toast.error(response.message || "Failed to send OTP");
      }
    } catch (error) {
      console.error("Failed to send OTP:", error);
      toast.error("Failed to send OTP");
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtp = async (otp: string): Promise<boolean> => {
    try {
      const response: ApiResponse = await apiClient({
        method: "POST",
        endpoint: "auth/verify-otp-by-email",
        data: {
          email: email,
          otp: otp,
        },
      });

      if (response.statusCode === 200) {
        await tokenManager.setToken(response.data.token, response.data.user);
        setIsAuthenticated(true);
        setShowOtpModal(false);
        await loadOrders(response.data.token);
        toast.success("Successfully authenticated!");
        return true;
      } else {
        toast.error(response.message || "Invalid OTP");
        return false;
      }
    } catch (error) {
      console.error("OTP verification failed:", error);
      toast.error("OTP verification failed");
      return false;
    }
  };

  const loadOrders = async (token: string) => {
    setIsLoadingOrders(true);
    try {
      const response: ApiResponse<PatientData> = await apiClient({
        method: "GET",
        endpoint: "auth/patient/orders",
        token,
      });

      if (response.statusCode === 200 && response.data) {
        setOrders(response.data.orders);
      } else {
        // For now, show empty state if API doesn't exist
        setOrders([]);
        console.log("Orders API not implemented yet");
      }
    } catch (error) {
      console.error("Failed to load orders:", error);
      setOrders([]);
    } finally {
      setIsLoadingOrders(false);
    }
  };

  const handleStartChat = async (order: Order) => {
    try {
      const response = await apiClient({
        method: "POST",
        endpoint: "room/create/by-order-guid",
        data: {
          order_guid: order.order_guid,
          description: `Consultation room for order ${order.order_guid}`,
          sms_status: true,
        },
        token: (await tokenManager.getToken()) as string,
      });

      // if (response.statusCode === 200 && response.data?.room) {
      //   router.push(`/chat/patient/${response.data.room.id}`);
      // } else if (response.data?.roomUrl) {
      // If room already exists, use the provided URL
      const roomId = response.data.room.room_identifier;
      router.push(`/chat/patient/${roomId}`);
      // } else {
      //   toast.error("Failed to create chat room");
      // }
    } catch (error) {
      console.error("Failed to create room:", error);
      toast.error("Failed to create chat room");
    }
  };

  const handleResendOtp = async () => {
    await sendOtp();
  };

  // Show loading state
  if (isLoading) {
    return <Loader show={true} />;
  }

  // Show login form if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Patient Chat Portal Login
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Enter your email to access your consultations
            </p>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Email Verification</CardTitle>
              <CardDescription>
                We&apos;ll send you an OTP to verify your identity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Input
                  id="role"
                  value={role}
                  disabled
                  className="bg-gray-100"
                />
              </div>
              <Button
                onClick={sendOtp}
                disabled={isLoading || !email.trim()}
                className="w-full"
              >
                {isLoading ? "Sending..." : "Send OTP"}
              </Button>
            </CardContent>
          </Card>

          {/* OTP Modal */}
          <OtpModal
            isOpen={showOtpModal}
            onClose={() => setShowOtpModal(false)}
            onVerify={verifyOtp}
            onResendOtp={handleResendOtp}
            userEmail={email}
            title="Enter Verification Code"
            description="Please enter the OTP sent to your email address."
          />
        </div>
      </div>
    );
  }

  // Show consult list for authenticated users
  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Orders</h1>
            <p className="mt-2 text-gray-600">
              View and access your medical consultation orders
            </p>
          </div>
          <LogoutButton variant="outline" size="default" />
        </div>

        {isLoadingOrders ? (
          <Loader show={true} />
        ) : orders.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No orders found
              </h3>
              <p className="text-gray-600">
                You don&apos;t have any orders yet. When you have scheduled
                consultations, they will appear here.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {orders.map((order) => (
                <li key={order.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {order.service_details.display_service_name}
                          </h3>
                          <p className="text-sm text-gray-500 mt-1">
                            {order.service_details.service_description}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={
                              order.status === "accept"
                                ? "default"
                                : order.status === "payment_pending"
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {order.status.replace("_", " ").toUpperCase()}
                          </Badge>
                          <span className="text-lg font-semibold text-gray-900">
                            ${order.service_details.service_amount}
                          </span>
                        </div>
                      </div>

                      <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center text-sm text-gray-600">
                            <User className="h-4 w-4 mr-2" />
                            {order.provider_details.name
                              ? `Dr. ${order.provider_details.name}`
                              : "Provider not assigned"}
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            {new Date(order.created_at).toLocaleDateString()}
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <Clock className="h-4 w-4 mr-2" />
                            {new Date(order.created_at).toLocaleTimeString()}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">Order ID:</span>{" "}
                            {order.id}
                          </div>
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">Session Type:</span>{" "}
                            {order.session_type}
                          </div>
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">Service Type:</span>{" "}
                            {order.service_type}
                          </div>
                        </div>
                      </div>

                      {order.pharmacy_details && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-md">
                          <h4 className="text-sm font-medium text-gray-900">
                            Pharmacy Details
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {order.pharmacy_details.name} -{" "}
                            {order.pharmacy_details.phone}
                          </p>
                          <p className="text-sm text-gray-600">
                            {order.pharmacy_details.address}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="ml-6 flex-shrink-0">
                      <Button
                        onClick={() => handleStartChat(order)}
                        disabled={order.status !== "accept"}
                        className="w-full"
                      >
                        {order.status === "accept"
                          ? "Start Chat"
                          : "Not Available"}
                      </Button>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientConsultListComponent;
