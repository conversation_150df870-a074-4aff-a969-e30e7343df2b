"use client";
import React, { Suspense, useContext, useEffect, useState } from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import { CheckCircle, RotateCcw } from "lucide-react";
import {
  ChatContainer,
  ConversationHeader,
  Message,
  MessageList,
  Avatar,
  MessageInput,
} from "@chatscope/chat-ui-kit-react";

import { apiClient } from "@/lib/api";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { validateFile, isImageType } from "@/lib/utils";
import FileDisplayContainer from "../fileDisplayContainer";
import { Loader } from "../shared/loader";
import { SocketContext } from "@/context/socket";
import useNotification from "@/hooks/useNotification";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import LogoutButton from "@/components/auth/logout-button";
import { useRouter } from "next/navigation";

type User = {
  email: string;
  first_name: string;
  last_name: string;
  status: string;
  user_guid: string;
  user_id: number;
};

const UserChatUICompoent = ({
  roomIdentifier,
  role,
  isConsult,
  token: accessToken,
}: {
  roomIdentifier: string;
  role: string;
  isConsult: boolean;
  token: string | null | undefined;
}) => {
  // const { lastMessage, sendJsonMessage, connectionStatus } = React.useContext(WebSocketContext);
  // const [messageHistory, setMessageHistory] = useState<MessageEvent<any>[]>([]);
  const [messageHistory, setMessageHistory] = useState<any[]>([]);
  const [isConsultOpen, setIsConsultOpen] = useState<boolean>(true);
  const [showPopup, setShowPopup] = useState<boolean>();
  const [otpModalVisible, setOtpModalVisible] = useState(false);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [imageModalValue, setImageModalValue] = useState("");
  const [otpModalValue, setOtpModalValue] = useState("");
  const [loader, setLoader] = useState(false);
  // const roomIdentifier = searchParams.get('room');
  const [user, setUser] = useState<User>();
  const [fullName, setFullName] = useState<string>();
  const [provider, setProvider] = useState<User>();
  const [roomId, setRoomId] = useState<string>();
  const [disableResendOtp, setDisableResendOtp] = useState(true);
  const { socket, isConnected } = useContext(SocketContext);

  const [wrongChat, setWrongChat] = useState(false);
  const router = useRouter();

  const sendNotification = useNotification();

  // socket?.on('receive-message', (data: any) => {
  //   const newMessage = [...(messageHistory ?? []), data];
  //   sendNotification({
  //     title: `New Message from ${fullName}`,
  //     body: data.message,
  //   })
  //   setMessageHistory(newMessage);
  // });
  const readAllMessage = async () => {
    // Use cookie-based token management instead of localStorage
    const { getAuthToken } = await import("@/lib/tokenManager");
    const token = await getAuthToken();
    await apiClient({
      method: "GET",
      endpoint: `chat/${roomIdentifier}/read-all-messages`,
      data: {},
      token: token || undefined,
    });
  };

  useEffect(() => {
    socket?.on("receive-message", (data: any) => {
      const newMessage = [...(messageHistory ?? []), data];
      sendNotification({
        title: `New Message from ${fullName}`,
        body: data.message,
      });
      readAllMessage();
      setMessageHistory(newMessage);
    });

    socket?.on("toggle-room-status", (data: any) => {
      const { room_status } = data;
      if (room_status === "inactive") {
        console.log("Chat room closed by provider");
        setIsConsultOpen(false);
        setShowPopup(true);
      } else {
        setIsConsultOpen(true);
        setShowPopup(false);
      }
    });

    return () => {
      socket?.off("receive-message");
    };
  }, [socket, messageHistory]);

  const getMessageHistory = async (roomIdentifier: string) => {
    setLoader(true);
    try {
      // Use cookie-based token management instead of localStorage
      const { getAuthToken } = await import("@/lib/tokenManager");
      const token = await getAuthToken();

      const res = await apiClient({
        method: "GET",
        endpoint: "chat?roomId=" + roomIdentifier,
        data: {},
        token: token || undefined,
      });
      if (res.statusCode === 200) {
        toast.success("Chat room initiated.", {
          duration: 1000,
          closeButton: true,
        });
        setMessageHistory(res.data.messages);
        setUser(res.data.patient);
        setRoomId(res.data.room.id);

        if (res.data.room.active) {
          setIsConsultOpen(true);
          setShowPopup(false);
        } else {
          setIsConsultOpen(false);
          setShowPopup(true);
        }

        setProvider(res.data.provider);
        setFullName(
          res.data.provider.first_name + " " + res.data.provider.last_name,
        );
      }

      if (res.statusCode === 500) {
        toast.error(res.message, {
          duration: 1000,
          closeButton: true,
        });
        setOtpModalVisible(true);
        sendOtp();
      }
    } catch (error: any) {
      if (error?.response?.data?.statusCode === 400) {
        toast.error(error?.response?.data?.message, {
          duration: Infinity,
        });
        setWrongChat(true);
      } else if (error?.response?.data?.statusCode === 500) {
        // Clear invalid tokens and refresh
        const { removeAuthToken } = await import("@/lib/tokenManager");
        await removeAuthToken();
        router.refresh();
      } else {
        toast.error("Something went wrong.", {});
      }
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  const checkLogin = async () => {
    if (!isConsultOpen) return;
    // Use cookie-based token management instead of localStorage
    const { getAuthToken } = await import("@/lib/tokenManager");
    const token = await getAuthToken();
    if (!token) {
      setOtpModalVisible(true);
      sendOtp();
    } else {
      getMessageHistory(roomIdentifier ?? "");
      readAllMessage();
    }
  };

  useEffect(() => {
    checkLogin();
  }, []);

  const uploadImage = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    try {
      const file = e.target.files[0];
      const validation = validateFile(file);

      if (!validation.isValid) {
        toast(validation.error);
        return;
      }

      setLoader(true);

      const data = new FormData();
      data.append("file", e.target.files[0]);
      if (user?.user_id && roomId) {
        data.append("user_id", user?.user_id.toString());
        data.append("room_id", roomId?.toString() ?? "");
      }

      const res = await apiClient({
        method: "POST",
        endpoint: "files/upload",
        data,
        isFormData: true,
      });

      socket?.emit("send-message", {
        sender_id: provider?.user_id,
        message: "ATTACHMENT",
        type: "room",
        file_path: res.fileUrl,
        file_id: res.fileId,
        room_id: roomId,
        sender: {
          user_id: user?.user_id,
          name: user?.first_name + " " + user?.last_name,
        },
      });

      const newMessage = [
        ...messageHistory,
        {
          message: "ATTACHMENT",
          type: "room",
          file_id: res.fileId,
          file: {
            user_file_id: res.fileId,
            path: res.fileUrl,
          },
          sender: {
            user_id: user?.user_id,
            name: user?.first_name + " " + user?.last_name,
          },
        },
      ];

      setMessageHistory(newMessage);
      setLoader(false);
    } catch (error) {
      toast.error("Upload Failed");
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  const sendOtp = async () => {
    setDisableResendOtp(true);
    const res = await apiClient({
      method: "POST",
      endpoint: "auth/send-otp",
      data: { room_identifier: roomIdentifier },
    });

    if (res.statusCode == 200) {
      setUser(res.data?.user);
      setRoomId(res.data?.room?.id);
      toast.success(res.message);
      setDisableResendOtp(false);
    } else {
      toast.error(res.message);
      setDisableResendOtp(false);
    }
  };

  const handledMessageSend = async (message: string) => {
    socket?.emit("send-message", {
      sender_id: user?.user_id,
      message: message,
      type: "room",
      room_id: roomId,
      sender: {
        user_id: user?.user_id,
        name: user?.first_name + " " + user?.last_name,
      },
    });

    const newMessage = [
      ...(messageHistory ?? []),
      {
        message: message,
        type: "room",
        roomId: roomId,
        sender: {
          user_id: user?.user_id,
          name: user?.first_name + " " + user?.last_name,
        },
      },
    ];
    setMessageHistory(newMessage);
  };

  const handleOnAttached = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*,.pdf,.doc,.docx";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        uploadImage({
          target: { files: [file] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      }
    };
    input.click();
  };

  useEffect(() => {
    if (user && roomId && socket) {
      socket.emit("join", {
        room_id: roomId,
        user_id: user?.user_id,
        name: "user",
      });
    }
  }, [user, provider, roomId]);

  const verifyUserOtp = async () => {
    if (!otpModalValue) {
      toast.error("Please enter otp");
      return;
    }
    const res = await apiClient({
      method: "POST",
      endpoint: "auth/verify-otp",
      data: { user_id: user?.user_guid, otp: otpModalValue },
    });
    if (res.statusCode === 200) {
      setOtpModalVisible(false);
      // Use cookie-based token management instead of localStorage
      const { setAuthToken } = await import("@/lib/tokenManager");
      await setAuthToken(res.data.token, res.data.user);
      toast.success(res.message);
      getMessageHistory(roomIdentifier ?? "");
    } else {
      toast.error(res.message);
    }
  };

  const getFileSignUrl = async (file_id: string) => {
    setLoader(true);

    try {
      // Use cookie-based token management instead of localStorage
      const { getAuthToken } = await import("@/lib/tokenManager");
      const token = await getAuthToken();
      const res = await apiClient({
        method: "POST",
        endpoint: "files/sign-url",
        data: { file_id },
        token: token || undefined,
      });

      setImageModalVisible(true);
      setImageModalValue(res.data.url);
      setLoader(false);
    } catch (error) {
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  return (
    <Suspense>
      <Loader show={loader} />
      <ChatContainer
        style={{
          height: "100vh",
        }}
      >
        <ConversationHeader>
          <Avatar
            name={fullName}
            src={`/images/${
              role === "PROVIDER" ? "patient.png" : "provider.png"
            }`}
          />
          <ConversationHeader.Content userName={fullName} />
          <ConversationHeader.Actions className="flex items-center gap-2">
            {isConnected && <CheckCircle />}
            <LogoutButton variant="ghost" size="icon" showText={false} />
          </ConversationHeader.Actions>
        </ConversationHeader>
        <MessageList>
          {messageHistory &&
            messageHistory?.map((data, idx) => {
              const isSendedByMe = data?.sender?.user_id == user?.user_id;
              const fullName = `${data.sender.first_name} ${data.sender.last_name}`;
              const created_at = new Date(data.created_at);
              const formattedTime = `${created_at.toLocaleDateString([], {
                year: "numeric",
                month: "short",
                day: "2-digit",
              })}, ${created_at.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
                hour12: true,
              })}`;
              return (
                <Message
                  onClick={() => {
                    if (data?.message === "ATTACHMENT") {
                      getFileSignUrl(data.file?.path);
                    }
                  }}
                  key={idx}
                  model={{
                    type: "html",
                    direction: isSendedByMe ? "outgoing" : "incoming",
                    message: data?.message?.replace(/<[^>]*>?/gm, ""),
                    position: "last",
                    sender: provider?.first_name + " " + provider?.last_name,
                    // sentTime: '15 mins ago',
                  }}
                >
                  {data?.message === "ATTACHMENT" ? (
                    <Message.ImageContent
                      className="cursor-pointer"
                      src={`/images/${
                        isImageType(data.file?.path) ? "file.png" : "pdf.png"
                      }`}
                      height={100}
                    />
                  ) : (
                    <Message.HtmlContent
                      html={`<b>${fullName}: </b> <br/>${data?.message} <br/><em style='font-size:10px'>${formattedTime}</em>`}
                    />
                  )}

                  {isSendedByMe ? (
                    <></>
                  ) : (
                    <Avatar
                      name={data.user?.first_name + " " + data.user?.last_name}
                      src={`/images/${
                        role === "PROVIDER" ? "patient.png" : "provider.png"
                      }`}
                    />
                  )}
                </Message>
              );
            })}
        </MessageList>
        {isConsultOpen && (
          <MessageInput
            autoFocus
            placeholder="Type message here"
            onSend={handledMessageSend}
            onAttachClick={handleOnAttached}
          />
        )}
      </ChatContainer>
      <Dialog open={otpModalVisible}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Enter OTP</DialogTitle>
            <DialogDescription>
              Enter the OTP sent to your email.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4 py-4 justify-center items-center">
            <InputOTP
              maxLength={6}
              value={otpModalValue}
              pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
              onChange={(value) => setOtpModalValue(value)}
            >
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
              </InputOTPGroup>
              <InputOTPSeparator />
              <InputOTPGroup>
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              disabled={disableResendOtp}
              onClick={() => sendOtp()}
            >
              <RotateCcw className="mr-2 h-4 w-4" /> Resend OTP
            </Button>
            <Button
              variant="default"
              type="submit"
              onClick={() => verifyUserOtp()}
            >
              Submit
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* <Dialog open={imageModalVisible} onOpenChange={() => setImageModalVisible(false)}> */}
      {/* <DialogContent className='sm:max-w-[925px]'> */}
      <FileDisplayContainer
        src={imageModalValue}
        imageModalVisible={imageModalVisible}
        setImageModalVisible={setImageModalVisible}
      />
      {/* </DialogContent> */}
      {/* </Dialog> */}
      <AlertDialog open={showPopup}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Consultation Chat Session Closed
            </AlertDialogTitle>
            <AlertDialogDescription>
              The chat session has been closed by the provider. You can not send
              any more messages in this chat. If you have any further questions
              or concerns, please contact the support team.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowPopup(false)}
              className={buttonVariants({ variant: "default" })}
            >
              Okay
            </AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={wrongChat}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Wrong Chat Link!!!</AlertDialogTitle>
            <AlertDialogDescription>
              You open a wrong chat. Please check the link and open correct
              Link.
            </AlertDialogDescription>
          </AlertDialogHeader>
          {/* <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setShowPopup(false);
                localStorage.clear();
                router.refresh();
                
              }}
              className={buttonVariants({ variant: 'default' })}
            >
              Okay
            </AlertDialogCancel>
          </AlertDialogFooter> */}
        </AlertDialogContent>
      </AlertDialog>
    </Suspense>
  );
};

export default UserChatUICompoent;
