/**
 * Shared type definitions for chat functionality
 * Consolidates duplicate type definitions across components
 */

export interface User {
  user_id: string;
  user_guid?: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  role?: string;
  status?: string;
  messageHistory?: any[];
}

export interface Provider extends User {
  // Provider-specific properties can be added here
}

export interface Patient extends User {
  // Patient-specific properties can be added here
}

export interface Room {
  id: string | number;
  room_identifier?: string;
  room_name?: string;
  description?: string;
  active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface FileAttachment {
  path: string;
  name?: string;
  size?: number;
  type?: string;
  url?: string;
}

export interface ChatMessage {
  id?: string | number;
  message: string;
  sender: User;
  receiver?: User;
  file?: FileAttachment;
  created_at?: string;
  updated_at?: string;
  room_id?: string | number;
}

export interface Service {
  service_key: string;
  service_name: string;
  display_service_name: string;
  service_type?: string;
  service_mode?: string;
  amount?: number;
}

export interface ChatMetadata {
  room: Room;
  participants: {
    patient: Patient;
    provider: Provider;
  };
  service: Service;
}

export interface ChatRoom {
  id: string | number;
  room: Room;
  patient: Patient;
  provider: Provider;
  messages: ChatMessage[];
}

export interface ChatApiResponse {
  messages: ChatMessage[];
  chatMetadata: ChatMetadata;
}

export type UserRole = "PATIENT" | "PROVIDER";

export interface ChatComponentProps {
  token?: string;
  patient: Patient;
  provider: Provider;
  room: Room;
  role: UserRole;
  roomIdentifier?: string;
}

export interface ApiResponse<T = any> {
  statusCode: number;
  message: string;
  data?: T;
  error?: string;
}

export interface AuthResponse {
  token: string;
  user: User;
  expiresAt?: string;
}

export interface OtpVerificationRequest {
  user_id: string;
  otp: string;
}

export interface FileUploadResponse {
  file_id: string;
  url: string;
  path: string;
}

export interface SignUrlRequest {
  file_id: string;
}

export interface SignUrlResponse {
  url: string;
}

// Socket event types
export interface SocketMessage {
  type: "message" | "file" | "status";
  data: ChatMessage;
  room_id: string | number;
}

export interface SocketJoinRoom {
  room_id: string | number;
  user_id: string;
  role: UserRole;
}

// Form validation types
export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

// Chat UI state types
export interface ChatUIState {
  messageHistory: ChatMessage[];
  isConsultOpen: boolean;
  showPopup: boolean;
  otpModalVisible: boolean;
  imageModalVisible: boolean;
  imageModalValue: string;
  otpModalValue: string;
  loader: boolean;
  wrongChat: boolean;
  disableResendOtp: boolean;
}

// Hook return types
export interface UseChat {
  messageHistory: ChatMessage[];
  sendMessage: (message: string) => Promise<void>;
  uploadFile: (file: File) => Promise<void>;
  getFileSignUrl: (fileId: string) => Promise<string | null>;
  loadMessageHistory: () => Promise<void>;
  markMessagesAsRead: () => Promise<void>;
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  clearError: () => void;
}

export interface UseAuth {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (token: string, user: User) => Promise<void>;
  logout: () => Promise<void>;
  verifyOtp: (userId: string, otp: string) => Promise<boolean>;
  sendOtp: (userId: string) => Promise<boolean>;
  refreshToken: () => Promise<boolean>;
  isLoading: boolean;
  error: string | null;
  clearError: () => void;
}

// Constants
export const CHAT_EVENTS = {
  JOIN_ROOM: "join-room",
  LEAVE_ROOM: "leave-room",
  SEND_MESSAGE: "send-message",
  RECEIVE_MESSAGE: "receive-message",
  USER_TYPING: "user-typing",
  USER_STOPPED_TYPING: "user-stopped-typing",
  USER_ONLINE: "user-online",
  USER_OFFLINE: "user-offline",
} as const;

export const FILE_VALIDATION = {
  MAX_SIZE: 20 * 1024 * 1024, // 20MB
  ALLOWED_EXTENSIONS: ["jpg", "jpeg", "png", "pdf", "doc", "docx", "heic"],
  IMAGE_EXTENSIONS: ["jpg", "jpeg", "png", "heic"],
} as const;

export const API_ENDPOINTS = {
  CHAT: "chat",
  AUTH: {
    VERIFY_OTP: "auth/verify-otp",
    SEND_OTP: "auth/send-otp",
  },
  FILES: {
    UPLOAD: "files/upload",
    SIGN_URL: "files/sign-url",
  },
  ROOM: {
    METADATA: "chat/{roomId}/metadata",
    EXISTS_CONSULT: "room/{roomId}/exits-consult",
  },
} as const;
