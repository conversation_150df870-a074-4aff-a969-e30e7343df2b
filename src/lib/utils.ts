import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { FILE_VALIDATION, FileValidationResult } from "@/types/chat";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * File validation utilities - consolidated and optimized
 */

export const getFileExtension = (filename: string): string => {
  return filename?.toLowerCase().split(".").pop() || "";
};

export const isImageType = (filename: string): boolean => {
  const ext = getFileExtension(filename);
  return (FILE_VALIDATION.IMAGE_EXTENSIONS as unknown as string[]).includes(
    ext,
  );
};

export const validateFileExtension = (
  filename: string,
): FileValidationResult => {
  const ext = getFileExtension(filename);
  const isValid = (
    FILE_VALIDATION.IMAGE_EXTENSIONS as unknown as string[]
  ).includes(ext);

  return {
    isValid,
    error: isValid
      ? undefined
      : `File type .${ext} is not allowed. Allowed types: ${FILE_VALIDATION.ALLOWED_EXTENSIONS.join(
          ", ",
        )}`,
  };
};

export const validateFileSize = (file: File): FileValidationResult => {
  const isValid = file.size <= FILE_VALIDATION.MAX_SIZE;
  const maxSizeMB = FILE_VALIDATION.MAX_SIZE / (1024 * 1024);

  return {
    isValid,
    error: isValid ? undefined : `File size exceeds ${maxSizeMB}MB limit`,
  };
};

export const validateFile = (file: File): FileValidationResult => {
  const extensionValidation = validateFileExtension(file.name);
  if (!extensionValidation.isValid) {
    return extensionValidation;
  }

  const sizeValidation = validateFileSize(file);
  if (!sizeValidation.isValid) {
    return sizeValidation;
  }

  return { isValid: true };
};

/**
 * Legacy functions for backward compatibility
 * @deprecated Use validateFileExtension instead
 */
export const checkFilesExtNotValidForImage = (file: string): boolean => {
  return !validateFileExtension(file).isValid;
};

/**
 * @deprecated Use validateFileSize instead
 */
export const checkFileSizeNotValidForImage = (file: File): boolean => {
  return !validateFileSize(file).isValid;
};

/**
 * Format user full name
 */
export const formatUserName = (firstName: string, lastName: string): string => {
  return `${firstName} ${lastName}`.trim();
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Sanitize HTML content
 */
export const sanitizeHtml = (html: string): string => {
  return html.replace(/<[^>]*>?/gm, "");
};

/**
 * Format date for chat messages with fallback handling
 */
export const formatChatMessageDate = (dateString?: string): string => {
  const date = dateString ? new Date(dateString) : new Date();
  // Fallback to current date if the parsed date is invalid
  const validDate = isNaN(date.getTime()) ? new Date() : date;

  return `${validDate.toLocaleDateString([], {
    year: "numeric",
    month: "short",
    day: "2-digit",
  })}, ${validDate.toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  })}`;
};

/**
 * Legacy file type functions for backward compatibility
 * @deprecated Use getFileExtension and isImageType instead
 */
export const getFileType = (fileUrl: string) => {
  const extention = fileUrl?.toLowerCase().split(".").pop();
  const ext = extention?.toLowerCase().split("?").shift();
  return ext;
};

export const isPdfType = (fileUrl: string) => {
  const ext = getFileType(fileUrl);
  return ext === "pdf";
};

export const isDocType = (fileUrl: string) => {
  const ext = getFileType(fileUrl);
  return ext === "doc" || ext === "docx";
};
